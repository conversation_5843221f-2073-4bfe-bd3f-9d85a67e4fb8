-- Create chat tables for live chat functionality
-- Migration script for Flyway

-- Create chat_room table
CREATE TABLE chat_room (
    id BIGSERIAL PRIMARY KEY,
    tenant_id VARCHAR(36) NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255),
    type VA<PERSON><PERSON><PERSON>(20) NOT NULL DEFAULT 'DIRECT', -- DIRECT, GROUP
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Create chat_participant table
CREATE TABLE chat_participant (
    id BIGSERIAL PRIMARY KEY,
    chat_room_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_read_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    
    CONSTRAINT fk_chat_participant_room FOREIGN KEY (chat_room_id) REFERENCES chat_room(id),
    CONSTRAINT fk_chat_participant_user FOREIGN KEY (user_id) REFERENCES g_user(id),
    CONSTRAINT uk_chat_participant UNIQUE (chat_room_id, user_id)
);

-- Create chat_message table
CREATE TABLE chat_message (
    id BIGSERIAL PRIMARY KEY,
    chat_room_id BIGINT NOT NULL,
    sender_id BIGINT NOT NULL,
    content TEXT NOT NULL,
    message_type VARCHAR(20) NOT NULL DEFAULT 'TEXT', -- TEXT, IMAGE, FILE
    file_url VARCHAR(500),
    file_name VARCHAR(255),
    file_size BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    
    CONSTRAINT fk_chat_message_room FOREIGN KEY (chat_room_id) REFERENCES chat_room(id),
    CONSTRAINT fk_chat_message_sender FOREIGN KEY (sender_id) REFERENCES g_user(id)
);

-- Create indexes for better performance
CREATE INDEX idx_chat_room_tenant_id ON chat_room(tenant_id);
CREATE INDEX idx_chat_room_created_by ON chat_room(created_by);
CREATE INDEX idx_chat_participant_room_id ON chat_participant(chat_room_id);
CREATE INDEX idx_chat_participant_user_id ON chat_participant(user_id);
CREATE INDEX idx_chat_message_room_id ON chat_message(chat_room_id);
CREATE INDEX idx_chat_message_sender_id ON chat_message(sender_id);
CREATE INDEX idx_chat_message_created_at ON chat_message(created_at);

-- Add comments
COMMENT ON TABLE chat_room IS 'Chat rooms for live chat functionality';
COMMENT ON TABLE chat_participant IS 'Users participating in chat rooms';
COMMENT ON TABLE chat_message IS 'Messages sent in chat rooms';
