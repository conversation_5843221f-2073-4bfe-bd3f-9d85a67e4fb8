import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef } from '@angular/core';
import { ChatService, ChatRoom, ChatMessage, ChatUser, ChatMessageRequest } from '../../../core/services/chat.service';
import { UserService } from '../../../core/services/user.service';
import { UserRes } from '../../../model/response/user-res.model';
import { Subscription } from 'rxjs';

import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';

@Component({
  selector: 'app-chat',
  templateUrl: './chat.component.html',
  styleUrls: ['./chat.component.scss'],
  standalone: true,
  imports: [ CommonModule, TranslateModule, FormsModule, IconsModule ]
})
export class ChatComponent implements OnI<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> {
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('messageInput') messageInput!: ElementRef;

  // Support chat properties
  supportMessages: ChatMessage[] = [];
  newMessage: string = '';
  isLoading = false;
  isChatOpen = false;
  currentUser: UserRes | undefined;

  private subscriptions: Subscription[] = [];

  constructor(
    private chatService: ChatService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.loadCurrentUser();
    this.loadSupportMessages();
    this.subscribeToMessages();
  }

  private loadCurrentUser(): void {
    const userSub = this.userService.user$.subscribe(user => {
      this.currentUser = user;
    });
    this.subscriptions.push(userSub);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.chatService.disconnect();
  }

  private subscribeToMessages(): void {
    const messagesSub = this.chatService.messages$.subscribe(messages => {
      // Update support messages when new messages arrive
      this.loadSupportMessages();
    });
    this.subscriptions.push(messagesSub);
  }

  loadSupportMessages(): void {
    this.isLoading = true;
    const sub = this.chatService.getSupportMessages().subscribe({
      next: (response) => {
        console.log('Support messages response:', response);
        if (response.success) {
          this.supportMessages = response.data.content || response.data || [];
          console.log('Loaded support messages:', this.supportMessages);
          this.scrollToBottom();
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading support messages:', error);
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  sendSupportMessage(): void {
    if (!this.newMessage.trim()) {
      return;
    }

    this.isLoading = true;
    const messageRequest: ChatMessageRequest = {
      chat_room_id: 0, // Will be handled by backend for support messages
      content: this.newMessage.trim(),
      message_type: 'TEXT'
    };

    const sub = this.chatService.sendSupportMessage(messageRequest).subscribe({
      next: (response) => {
        if (response.success) {
          this.newMessage = '';
          this.loadSupportMessages(); // Reload messages to show the new one
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error sending support message:', error);
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  toggleChat(): void {
    this.isChatOpen = !this.isChatOpen;
    if (this.isChatOpen) {
      this.loadSupportMessages();
    }
  }

  onKeyPress(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendSupportMessage();
    }
  }

  private scrollToBottom(): void {
    setTimeout(() => {
      if (this.messagesContainer) {
        const element = this.messagesContainer.nativeElement;
        element.scrollTop = element.scrollHeight;
      }
    }, 100);
  }

  getRoomDisplayName(room: ChatRoom): string {
    if (room.type === 'DIRECT' && room.participants) {
      // For direct chats, show the other participant's name
      const otherParticipant = room.participants.find(p => p.user_id !== this.getCurrentUserId());
      return otherParticipant?.user?.user_name || room.name || 'Direct Chat';
    }
    return room.name || 'Group Chat';
  }

  getCurrentUserId(): number {
    return this.currentUser?.id || 0;
  }

  formatMessageTime(timestamp: string): string {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  getTotalUnreadCount(): number {
    // For support chat, we can return 0 for now
    // Later we can implement unread count for support messages
    return 0;
  }
}
