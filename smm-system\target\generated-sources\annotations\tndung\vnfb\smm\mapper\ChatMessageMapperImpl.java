package tndung.vnfb.smm.mapper;

import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.chat.ChatMessageReq;
import tndung.vnfb.smm.dto.chat.ChatMessageRes;
import tndung.vnfb.smm.entity.ChatMessage;
import tndung.vnfb.smm.entity.ChatMessage.ChatMessageBuilder;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class ChatMessageMapperImpl implements ChatMessageMapper {

    @Autowired
    private GUserMapper gUserMapper;

    @Override
    public ChatMessage toEntity(ChatMessageReq req) {
        if ( req == null ) {
            return null;
        }

        ChatMessageBuilder chatMessage = ChatMessage.builder();

        chatMessage.chatRoomId( req.getChatRoomId() );
        chatMessage.content( req.getContent() );
        chatMessage.fileName( req.getFileName() );
        chatMessage.fileSize( req.getFileSize() );
        chatMessage.fileUrl( req.getFileUrl() );
        chatMessage.messageType( req.getMessageType() );

        chatMessage.isDeleted( false );

        return chatMessage.build();
    }

    @Override
    public ChatMessageRes toDTO(ChatMessage entity) {
        if ( entity == null ) {
            return null;
        }

        ChatMessageRes chatMessageRes = new ChatMessageRes();

        chatMessageRes.setChatRoomId( entity.getChatRoomId() );
        chatMessageRes.setContent( entity.getContent() );
        chatMessageRes.setCreatedAt( entity.getCreatedAt() );
        chatMessageRes.setFileName( entity.getFileName() );
        chatMessageRes.setFileSize( entity.getFileSize() );
        chatMessageRes.setFileUrl( entity.getFileUrl() );
        chatMessageRes.setId( entity.getId() );
        chatMessageRes.setMessageType( entity.getMessageType() );
        chatMessageRes.setSender( gUserMapper.toApiKey( entity.getSender() ) );
        chatMessageRes.setSenderId( entity.getSenderId() );

        return chatMessageRes;
    }
}
