package tndung.vnfb.smm.mapper;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.dto.chat.ChatParticipantRes;
import tndung.vnfb.smm.dto.chat.ChatRoomReq;
import tndung.vnfb.smm.dto.chat.ChatRoomRes;
import tndung.vnfb.smm.entity.ChatParticipant;
import tndung.vnfb.smm.entity.ChatRoom;
import tndung.vnfb.smm.entity.ChatRoom.ChatRoomBuilder;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor"
)
@Component
public class ChatRoomMapperImpl implements ChatRoomMapper {

    @Autowired
    private ChatParticipantMapper chatParticipantMapper;
    @Autowired
    private GUserMapper gUserMapper;

    @Override
    public ChatRoom toEntity(ChatRoomReq req) {
        if ( req == null ) {
            return null;
        }

        ChatRoomBuilder chatRoom = ChatRoom.builder();

        chatRoom.name( req.getName() );
        chatRoom.type( req.getType() );

        chatRoom.isActive( true );

        return chatRoom.build();
    }

    @Override
    public ChatRoomRes toDTO(ChatRoom entity) {
        if ( entity == null ) {
            return null;
        }

        ChatRoomRes chatRoomRes = new ChatRoomRes();

        chatRoomRes.setCreatedAt( entity.getCreatedAt() );
        chatRoomRes.setCreatedBy( entity.getCreatedBy() );
        chatRoomRes.setCreator( gUserMapper.toApiKey( entity.getCreator() ) );
        chatRoomRes.setId( entity.getId() );
        chatRoomRes.setIsActive( entity.getIsActive() );
        chatRoomRes.setName( entity.getName() );
        chatRoomRes.setParticipants( chatParticipantListToChatParticipantResList( entity.getParticipants() ) );
        chatRoomRes.setType( entity.getType() );
        chatRoomRes.setUpdatedAt( entity.getUpdatedAt() );

        return chatRoomRes;
    }

    protected List<ChatParticipantRes> chatParticipantListToChatParticipantResList(List<ChatParticipant> list) {
        if ( list == null ) {
            return null;
        }

        List<ChatParticipantRes> list1 = new ArrayList<ChatParticipantRes>( list.size() );
        for ( ChatParticipant chatParticipant : list ) {
            list1.add( chatParticipantMapper.toDTO( chatParticipant ) );
        }

        return list1;
    }
}
