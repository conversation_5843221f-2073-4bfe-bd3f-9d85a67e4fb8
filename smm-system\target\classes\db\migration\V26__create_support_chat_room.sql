-- Create support chat room for support messages
-- Migration script for Flyway

-- First, check if the support room already exists
DO $$
BEGIN
    -- Insert a special chat room for support messages if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM chat_room WHERE id = -1) THEN
        INSERT INTO chat_room (id, tenant_id, name, type, created_by, created_at, updated_at, is_active)
        VALUES (-1, 'SYSTEM', 'Support Chat Room', 'GROUP', 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, true);
    END IF;
END $$;

-- Update the sequence to avoid conflicts with the special ID
-- Only update if there are positive IDs in the table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM chat_room WHERE id > 0) THEN
        PERFORM setval('chat_room_id_seq', GREATEST((SELECT MAX(id) FROM chat_room WHERE id > 0), 1));
    ELSE
        PERFORM setval('chat_room_id_seq', 1);
    END IF;
END $$;
